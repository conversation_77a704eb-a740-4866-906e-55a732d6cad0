/**
 * UAUI Navigation Guide Component
 * Provides step-by-step guidance for UAUI system setup and usage
 */

"use client";

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Sparkles,
  ArrowRight,
  CheckCircle,
  Circle,
  Bot,
  Settings,
  Beaker,
  Brain,
  Zap
} from 'lucide-react';
import { useUAUI } from '@/uaui/use-uaui';
import { useAIProviderStore } from '@/stores/ai-provider-store';

export default function UAUINavigationGuide() {
  const { isInitialized } = useUAUI();
  const { providers } = useAIProviderStore();

  const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
  const hasProviders = activeProviders.length > 0;

  const steps = [
    {
      id: 'providers',
      title: 'Configure AI Providers',
      description: 'Set up your AI providers (OpenAI, Claude, Gemini, etc.)',
      href: '/dashboard/ai-providers',
      icon: Bot,
      completed: hasProviders,
      badge: hasProviders ? 'Complete' : 'Required'
    },
    {
      id: 'initialize',
      title: 'Initialize UAUI System',
      description: 'Start the Universal AI User Interface',
      href: '/dashboard/uaui-system',
      icon: Sparkles,
      completed: isInitialized,
      badge: isInitialized ? 'Active' : 'Pending',
      disabled: !hasProviders
    },
    {
      id: 'test',
      title: 'Test UAUI Console',
      description: 'Try out the UAUI testing interface',
      href: '/dashboard/uaui-system/test',
      icon: Beaker,
      completed: false,
      badge: 'Optional',
      disabled: !isInitialized
    },
    {
      id: 'templates',
      title: 'Manage Prompt Templates',
      description: 'Create and manage AI prompt templates',
      href: '/dashboard/prompt-templates',
      icon: Brain,
      completed: false,
      badge: 'Advanced',
      disabled: !isInitialized
    }
  ];

  const getStepIcon = (step: typeof steps[0]) => {
    if (step.completed) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    if (step.disabled) {
      return <Circle className="h-5 w-5 text-muted-foreground" />;
    }
    return <step.icon className="h-5 w-5 text-primary" />;
  };

  const getBadgeVariant = (step: typeof steps[0]) => {
    if (step.completed) return 'default';
    if (step.disabled) return 'secondary';
    if (step.badge === 'Required') return 'destructive';
    return 'outline';
  };

  return (
    <Card className="card-enhanced">
      <CardHeader className="bg-gradient-to-r from-card to-muted/20 border-b border-border/50">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Zap className="h-5 w-5 text-primary" />
          UAUI Quick Start Guide
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Follow these steps to set up and start using the Universal AI User Interface
        </p>
      </CardHeader>

      <CardContent className="p-6">
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div key={step.id}>
              <div className="flex items-center justify-between p-4 rounded-lg border border-border/50 hover:border-border transition-colors">
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                    {getStepIcon(step)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className={`font-medium ${step.disabled ? 'text-muted-foreground' : ''}`}>
                        {step.title}
                      </h4>
                      <Badge variant={getBadgeVariant(step)} className="text-xs">
                        {step.badge}
                      </Badge>
                    </div>
                    <p className={`text-sm ${step.disabled ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                      {step.description}
                    </p>
                  </div>
                </div>

                <Link href={step.href}>
                  <Button
                    variant={step.completed ? "outline" : "default"}
                    size="sm"
                    disabled={step.disabled}
                    className="gap-2"
                  >
                    {step.completed ? 'Manage' : 'Start'}
                    <ArrowRight className="h-3 w-3" />
                  </Button>
                </Link>
              </div>

              {index < steps.length - 1 && (
                <div className="flex justify-center py-2">
                  <div className="w-px h-4 bg-border" />
                </div>
              )}
            </div>
          ))}
        </div>

        <Separator className="my-6" />

        <div className="text-center space-y-3">
          <h4 className="font-medium text-sm">Need Help?</h4>
          <div className="flex items-center justify-center gap-2">
            <Link href="/dashboard/uaui-system">
              <Button variant="outline" size="sm" className="gap-2">
                <Settings className="h-3 w-3" />
                System Overview
              </Button>
            </Link>
            <Link href="/dashboard/uaui-system/test">
              <Button variant="outline" size="sm" className="gap-2" disabled={!isInitialized}>
                <Beaker className="h-3 w-3" />
                Test Console
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
