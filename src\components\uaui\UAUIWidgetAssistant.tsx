/**
 * <PERSON>A<PERSON> Widget Assistant Component
 * AI-powered widget configuration assistant
 */

"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Sparkles,
  Send,
  Loader2,
  CheckCircle,
  AlertCircle,
  Wand2,
  MessageSquare
} from 'lucide-react';
import { useUAUI } from '@/uaui/use-uaui';
import type { WidgetConfigData } from '@/lib/validation';

interface UAUIWidgetAssistantProps {
  widgetId?: string;
  currentConfig?: Partial<WidgetConfigData>;
  onConfigUpdate?: (config: Partial<WidgetConfigData>) => void;
  onActionExecuted?: (action: any) => void;
}

interface AssistantMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  actions?: any[];
}

export default function UAUIWidgetAssistant({
  widgetId = 'preview-widget',
  currentConfig,
  onConfigUpdate,
  onActionExecuted
}: UAUIWidgetAssistantProps) {
  // UAUI Hook
  const {
    isInitialized,
    isLoading: uauiLoading,
    error: uauiError,
    sendMessage
  } = useUAUI();

  // Component State
  const [messages, setMessages] = useState<AssistantMessage[]>([
    {
      id: 'welcome',
      type: 'system',
      content: 'Hi! I\'m your UAUI Widget Assistant. I can help you configure your widget settings, suggest improvements, and answer questions about widget customization.',
      timestamp: Date.now()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isProcessing || !isInitialized) return;

    const userMessage: AssistantMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: inputMessage.trim(),
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsProcessing(true);

    try {
      // Prepare context for UAUI
      const context = {
        widgetId,
        currentConfig,
        messageHistory: messages.slice(-5), // Last 5 messages for context
        task: 'widget-configuration'
      };

      // Send message to UAUI
      const response = await sendMessage({
        id: `request-${Date.now()}`,
        message: inputMessage.trim(),
        context,
        metadata: {
          source: 'widget-assistant',
          widgetId
        }
      });

      if (response) {
        const assistantMessage: AssistantMessage = {
          id: response.id,
          type: 'assistant',
          content: response.message || 'No response message',
          timestamp: Date.now(),
          actions: response.actions
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Handle any actions returned by UAUI
        if (response.actions && response.actions.length > 0) {
          response.actions.forEach((action: any) => {
            if (action.type === 'update-config' && onConfigUpdate) {
              onConfigUpdate(action.data);
            }
            if (onActionExecuted) {
              onActionExecuted(action);
            }
          });
        }
      }
    } catch (error) {
      console.error('UAUI Widget Assistant error:', error);

      const errorMessage: AssistantMessage = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: Date.now()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getMessageIcon = (type: AssistantMessage['type']) => {
    switch (type) {
      case 'assistant':
        return <Sparkles className="h-4 w-4 text-primary" />;
      case 'user':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'system':
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
      default:
        return null;
    }
  };

  if (uauiError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            UAUI Assistant Unavailable
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              The UAUI system is not available. Please check your configuration and try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!isInitialized) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-muted-foreground" />
            UAUI Widget Assistant
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              UAUI system is not initialized. Please initialize UAUI to use the widget assistant.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[500px] flex flex-col">
      <CardHeader className="flex-shrink-0 border-b">
        <CardTitle className="flex items-center gap-2">
          <Wand2 className="h-5 w-5 text-primary" />
          UAUI Widget Assistant
          <Badge variant="secondary" className="text-xs">
            {isInitialized ? 'Active' : 'Inactive'}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'
                }`}
            >
              {message.type !== 'user' && (
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                  {getMessageIcon(message.type)}
                </div>
              )}

              <div
                className={`max-w-[80%] rounded-lg p-3 ${message.type === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : message.type === 'system'
                    ? 'bg-muted text-muted-foreground'
                    : 'bg-muted'
                  }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                {message.actions && message.actions.length > 0 && (
                  <div className="mt-2 pt-2 border-t border-border/50">
                    <p className="text-xs text-muted-foreground mb-1">Actions:</p>
                    {message.actions.map((action: any, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs mr-1">
                        {action.type}
                      </Badge>
                    ))}
                  </div>
                )}

                <p className="text-xs opacity-70 mt-1">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </p>
              </div>

              {message.type === 'user' && (
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                  {getMessageIcon(message.type)}
                </div>
              )}
            </div>
          ))}

          {isProcessing && (
            <div className="flex gap-3 justify-start">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin text-primary" />
              </div>
              <div className="bg-muted rounded-lg p-3">
                <p className="text-sm text-muted-foreground">Thinking...</p>
              </div>
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="flex-shrink-0 border-t p-4">
          <div className="flex gap-2">
            <Textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me about widget configuration..."
              className="flex-1 min-h-[40px] max-h-[120px] resize-none"
              disabled={isProcessing || !isInitialized}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isProcessing || !isInitialized}
              size="sm"
              className="gap-2"
            >
              {isProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
