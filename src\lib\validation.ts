/**
 * Validation schemas and types for UAUI system
 */

export interface WidgetConfigData {
  id: string;
  name: string;
  type: string;
  settings: Record<string, any>;
  enabled: boolean;
}

export function validateWidgetConfig(config: any): config is WidgetConfigData {
  return (
    typeof config === 'object' &&
    typeof config.id === 'string' &&
    typeof config.name === 'string' &&
    typeof config.type === 'string' &&
    typeof config.settings === 'object' &&
    typeof config.enabled === 'boolean'
  );
}
