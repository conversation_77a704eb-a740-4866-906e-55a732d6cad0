/**
 * React Hook for UAUI Integration
 * Provides easy access to UAUI functionality in React components
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAIProviderStore } from '@/stores/ai-provider-store';
import uauiIntegrationService, {
  type ChatMessage,
  type UAUIActionResult
} from './uaui-integration-service';
import type { UAUIResponse } from '..';

interface UseUAUIOptions {
  autoInitialize?: boolean;
  environment?: 'development' | 'staging' | 'production';
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

interface UseUAUIReturn {
  // State
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  availableProviders: string[];

  // Actions
  initialize: () => Promise<boolean>;
  processChatMessage: (message: ChatMessage) => Promise<UAUIResponse | null>;
  sendMessage: (request: any) => Promise<UAUIResponse | null>;
  reinitialize: () => Promise<boolean>;
  shutdown: () => Promise<void>;
  clearError: () => void;

  // Utilities
  isReady: () => boolean;
  getConfig: () => any;
}

export function useUAUI(options: UseUAUIOptions = {}): UseUAUIReturn {
  const {
    autoInitialize = true,
    environment = 'development',
    logLevel = 'info'
  } = options;

  // State
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableProviders, setAvailableProviders] = useState<string[]>([]);

  // Refs
  const initializationAttempted = useRef(false);

  // AI Provider store
  const { providers, fetchProviders } = useAIProviderStore();

  /**
   * Initialize UAUI
   */
  const initialize = useCallback(async (): Promise<boolean> => {
    if (isLoading) return false;

    setIsLoading(true);
    setError(null);

    try {
      // Ensure we have latest providers
      await fetchProviders();

      // Initialize UAUI service
      const success = await uauiIntegrationService.initialize();

      if (success) {
        setIsInitialized(true);

        // Get available providers
        const providers = await uauiIntegrationService.getAvailableProviders();
        setAvailableProviders(providers);

        console.log('✅ UAUI hook initialized successfully');
      } else {
        setError('Failed to initialize UAUI - no active AI providers found');
      }

      return success;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to initialize UAUI';
      setError(errorMessage);
      console.error('❌ UAUI initialization failed:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [fetchProviders, isLoading]);

  /**
   * Process chat message with UAUI
   */
  const processChatMessage = useCallback(async (
    message: ChatMessage
  ): Promise<UAUIResponse | null> => {
    if (!isInitialized) {
      setError('UAUI not initialized');
      return null;
    }

    try {
      setError(null);
      const response = await uauiIntegrationService.processChatMessage(message);

      console.log('✅ UAUI processed message:', {
        provider: response.metadata.provider,
        responseTime: response.metadata.responseTime,
        actions: response.actions?.length || 0
      });

      return response;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to process message';
      setError(errorMessage);
      console.error('❌ UAUI message processing failed:', err);
      return null;
    }
  }, [isInitialized]);

  /**
   * Send a message to UAUI (wrapper around processChatMessage)
   */
  const sendMessage = useCallback(async (
    request: any
  ): Promise<UAUIResponse | null> => {
    const chatMessage: ChatMessage = {
      id: request.id || `msg-${Date.now()}`,
      message: request.message,
      widgetId: request.context?.widgetId || 'default',
      userId: request.context?.userId,
      sessionId: request.context?.sessionId,
      metadata: request.metadata
    };

    return await processChatMessage(chatMessage);
  }, [processChatMessage]);

  /**
   * Reinitialize UAUI (useful when providers change)
   */
  const reinitialize = useCallback(async (): Promise<boolean> => {
    setIsInitialized(false);
    setAvailableProviders([]);
    return await initialize();
  }, [initialize]);

  /**
   * Shutdown UAUI
   */
  const shutdown = useCallback(async (): Promise<void> => {
    try {
      await uauiIntegrationService.shutdown();
      setIsInitialized(false);
      setAvailableProviders([]);
      setError(null);
      console.log('✅ UAUI shut down successfully');
    } catch (err: any) {
      console.error('❌ UAUI shutdown failed:', err);
    }
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Check if UAUI is ready
   */
  const isReady = useCallback((): boolean => {
    return uauiIntegrationService.isReady();
  }, []);

  /**
   * Get UAUI configuration
   */
  const getConfig = useCallback(() => {
    return uauiIntegrationService.getConfig();
  }, []);

  // Auto-initialize on mount
  useEffect(() => {
    if (autoInitialize && !initializationAttempted.current && Array.isArray(providers) && providers.length > 0) {
      initializationAttempted.current = true;
      initialize();
    }
  }, [autoInitialize, initialize, providers]);

  // Listen for provider changes
  useEffect(() => {
    if (isInitialized && Array.isArray(providers) && providers.length > 0) {
      // Reinitialize when providers change
      const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
      if (activeProviders.length !== availableProviders.length) {
        console.log('🔄 AI providers changed, reinitializing UAUI...');
        reinitialize();
      }
    }
  }, [providers, isInitialized, availableProviders, reinitialize]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isInitialized) {
        shutdown();
      }
    };
  }, [isInitialized, shutdown]);

  return {
    // State
    isInitialized,
    isLoading,
    error,
    availableProviders,

    // Actions
    initialize,
    processChatMessage,
    sendMessage,
    reinitialize,
    shutdown,
    clearError,

    // Utilities
    isReady,
    getConfig
  };
}

/**
 * Hook for simple UAUI chat processing
 */
export function useUAUIChat(widgetId: string) {
  const uaui = useUAUI();

  const sendMessage = useCallback(async (
    message: string,
    userId?: string,
    sessionId?: string
  ): Promise<UAUIResponse | null> => {
    const chatMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      message,
      widgetId,
      userId,
      sessionId
    };

    return await uaui.processChatMessage(chatMessage);
  }, [uaui, widgetId]);

  return {
    ...uaui,
    sendMessage
  };
}

export default useUAUI;
