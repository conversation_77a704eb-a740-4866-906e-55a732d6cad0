/**
 * UAUI Integration Service for Tempo Widget
 * Bridges Tempo Widget's AI providers with the UAUI system
 */

import { UAUI, createUAUIFromExisting } from '../..';
import { useAIProviderStore } from '@/stores/ai-provider-store';
import type { UAUIResponse } from '../..';

// Types for integration
export interface ChatMessage {
  id: string;
  message: string;
  context?: any;
  metadata?: Record<string, any>;
}

export interface UAUIActionResult {
  success: boolean;
  data?: any;
  error?: string;
}

interface TempoUAUIConfig {
  environment?: 'development' | 'production';
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  selectionStrategy?: 'smart' | 'round-robin' | 'random';
}

/**
 * UAUI Integration Service
 * Manages UAUI lifecycle and integration with Tempo Widget system
 */
class UAUIIntegrationService {
  private uaui: UAUI | null = null;
  private isInitialized = false;
  private config: TempoUAUIConfig;

  constructor(config: TempoUAUIConfig = {
    environment: 'development',
    logLevel: 'info',
    selectionStrategy: 'smart'
  }) {
    this.config = config;
  }

  /**
   * Initialize UAUI with current AI providers from Tempo Widget
   */
  async initialize(): Promise<boolean> {
    try {
      // Get AI providers from Tempo Widget store
      // For now, use default providers since we can't call hooks here
      const providers = [
        { id: 'openai', name: 'OpenAI', type: 'openai', enabled: true, isActive: true, isConfigured: true },
        { id: 'groq', name: 'Groq', type: 'groq', enabled: true, isActive: true, isConfigured: true }
      ];

      // Filter active and configured providers
      const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];

      if (activeProviders.length === 0) {
        console.warn('No active AI providers found for UAUI initialization');
        return false;
      }

      // Convert Tempo providers to UAUI format
      const uauiProviders = activeProviders.map(provider => ({
        id: provider.id,
        name: provider.name,
        type: provider.type,
        apiKey: provider.apiKey,
        models: provider.models || [],
        defaultModel: provider.models?.[0] || 'default',
        maxTokens: 4000,
        temperature: 0.7,
        timeout: 30000
      }));

      console.log(`🔧 Converting ${uauiProviders.length} Tempo providers to UAUI format`);

      // Create UAUI instance
      this.uaui = createUAUIFromExisting(uauiProviders, {
        environment: this.config.environment || 'development',
        logLevel: this.config.logLevel || 'info',
        selectionStrategy: this.config.selectionStrategy || 'smart'
      });

      // Initialize UAUI
      await this.uaui.initialize();
      this.isInitialized = true;

      console.log('✅ UAUI Integration Service initialized successfully');
      return true;

    } catch (error) {
      console.error('❌ UAUI Integration Service initialization failed:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Reinitialize UAUI with updated providers
   */
  async reinitialize(): Promise<boolean> {
    console.log('🔄 Reinitializing UAUI Integration Service...');

    if (this.uaui) {
      await this.shutdown();
    }

    return await this.initialize();
  }

  /**
   * Shutdown UAUI
   */
  async shutdown(): Promise<void> {
    if (this.uaui) {
      await this.uaui.shutdown();
      this.uaui = null;
    }
    this.isInitialized = false;
    console.log('🛑 UAUI Integration Service shutdown complete');
  }

  /**
   * Send message to UAUI
   */
  async sendMessage(message: ChatMessage): Promise<UAUIResponse | null> {
    if (!this.isInitialized || !this.uaui) {
      throw new Error('UAUI Integration Service is not initialized');
    }

    try {
      const response = await this.uaui.processAIRequest({
        id: message.id,
        message: message.message,
        context: message.context || {},
        metadata: {
          source: 'tempo-widget',
          timestamp: Date.now(),
          ...message.metadata
        }
      });

      return response;
    } catch (error) {
      console.error('❌ UAUI message processing failed:', error);
      throw error;
    }
  }

  /**
   * Get available providers
   */
  getAvailableProviders(): string[] {
    if (!this.uaui) return [];

    try {
      return this.uaui.getAvailableProviders();
    } catch (error) {
      console.error('❌ Failed to get available providers:', error);
      return [];
    }
  }

  /**
   * Check if UAUI is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.uaui !== null;
  }

  /**
   * Get UAUI status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      hasInstance: this.uaui !== null,
      providers: this.getAvailableProviders(),
      config: this.config
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<TempoUAUIConfig>) {
    this.config = { ...this.config, ...newConfig };
  }
}

// Create singleton instance
const uauiIntegrationService = new UAUIIntegrationService();

export default uauiIntegrationService;
export { UAUIIntegrationService };
export type { TempoUAUIConfig };
