/**
 * UAUI Integration Service
 * Bridges Tempo Widget system with UAUI Protocol
 */

import {
  UAUI,
  createUAUIFromExisting,
  UAUIRequest,
  UAUIResponse,
  ExistingProviderConfig
} from '..';
// import { useAIProviderStore } from '@/stores/ai-provider-store';
import apiClient from '../lib/api-client';

interface TempoUAUIConfig {
  environment: 'development' | 'staging' | 'production';
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  selectionStrategy: 'smart' | 'round_robin' | 'least_latency';
}

interface ChatMessage {
  id: string;
  message: string;
  widgetId: string;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

interface UAUIActionResult {
  success: boolean;
  actionsExecuted: number;
  errors?: string[];
}

class UAUIIntegrationService {
  private uaui: UAUI | null = null;
  private isInitialized = false;
  private config: TempoUAUIConfig;

  constructor(config: TempoUAUIConfig = {
    environment: 'development',
    logLevel: 'info',
    selectionStrategy: 'smart'
  }) {
    this.config = config;
  }

  /**
   * Initialize UAUI with current AI providers from Tempo Widget
   */
  async initialize(): Promise<boolean> {
    try {
      // For now, use default providers since we can't call hooks here
      // In a real implementation, this would get providers from a store or API
      const activeProviders = [
        { id: 'openai', name: 'OpenAI', type: 'openai', enabled: true, isActive: true, isConfigured: true, apiKey: 'demo-key' },
        { id: 'groq', name: 'Groq', type: 'groq', enabled: true, isActive: true, isConfigured: true, apiKey: 'demo-key' }
      ];

      if (activeProviders.length === 0) {
        console.warn('No active AI providers found for UAUI initialization');
        return false;
      }

      // Convert Tempo providers to UAUI format
      const uauiProviders: ExistingProviderConfig[] = activeProviders.map(provider => ({
        id: provider.id,
        name: provider.name,
        type: provider.type as any,
        apiKey: provider.apiKey,
        defaultModel: this.getDefaultModel(provider.type),
        maxTokens: 4000,
        temperature: 0.7,
        timeout: 30000
      }));

      // Create UAUI instance
      this.uaui = createUAUIFromExisting(uauiProviders, {
        environment: this.config.environment,
        logLevel: this.config.logLevel,
        selectionStrategy: this.config.selectionStrategy
      });

      // Initialize UAUI
      await this.uaui.initialize();

      this.isInitialized = true;
      console.log(`✅ UAUI initialized with ${activeProviders.length} providers`);

      return true;
    } catch (error) {
      console.error('Failed to initialize UAUI:', error);
      return false;
    }
  }

  /**
   * Process chat message with UAUI intelligence
   */
  async processChatMessage(chatMessage: ChatMessage): Promise<UAUIResponse> {
    if (!this.isInitialized || !this.uaui) {
      throw new Error('UAUI not initialized. Call initialize() first.');
    }

    const request: UAUIRequest = {
      id: `chat-${Date.now()}`,
      type: 'ai.request',
      message: chatMessage.message,
      context: {
        app: 'tempo-widget',
        widgetId: chatMessage.widgetId,
        userId: chatMessage.userId,
        sessionId: chatMessage.sessionId,
        timestamp: Date.now()
      }
    };

    try {
      const response = await this.uaui.processAIRequest(request);

      // Execute any generated actions
      if (response.actions && response.actions.length > 0) {
        await this.executeActions(response.actions, chatMessage.widgetId);
      }

      return response;
    } catch (error) {
      console.error('UAUI chat processing failed:', error);
      throw error;
    }
  }

  /**
   * Execute UAUI-generated actions in Tempo Widget context
   */
  private async executeActions(actions: any[], widgetId: string): Promise<UAUIActionResult> {
    const results: UAUIActionResult = {
      success: true,
      actionsExecuted: 0,
      errors: []
    };

    for (const action of actions) {
      try {
        switch (action.type) {
          case 'widget.appearance.update':
            await this.updateWidgetAppearance(widgetId, action.payload);
            break;

          case 'widget.behavior.update':
            await this.updateWidgetBehavior(widgetId, action.payload);
            break;

          case 'cross_app.navigate':
            await this.handleNavigation(action.payload);
            break;

          case 'widget.ai.update':
            await this.updateWidgetAI(widgetId, action.payload);
            break;

          default:
            console.warn(`Unknown action type: ${action.type}`);
        }

        results.actionsExecuted++;
      } catch (error) {
        console.error(`Failed to execute action ${action.type}:`, error);
        results.errors?.push(`${action.type}: ${error}`);
        results.success = false;
      }
    }

    return results;
  }

  /**
   * Update widget appearance via API
   */
  private async updateWidgetAppearance(widgetId: string, appearance: any): Promise<void> {
    await apiClient.put(`/widgets/${widgetId}/appearance`, appearance);
    console.log(`✅ Updated widget ${widgetId} appearance:`, appearance);
  }

  /**
   * Update widget behavior via API
   */
  private async updateWidgetBehavior(widgetId: string, behavior: any): Promise<void> {
    await apiClient.put(`/widgets/${widgetId}/behavior`, behavior);
    console.log(`✅ Updated widget ${widgetId} behavior:`, behavior);
  }

  /**
   * Update widget AI settings via API
   */
  private async updateWidgetAI(widgetId: string, aiSettings: any): Promise<void> {
    await apiClient.put(`/widgets/${widgetId}/ai-settings`, aiSettings);
    console.log(`✅ Updated widget ${widgetId} AI settings:`, aiSettings);
  }

  /**
   * Handle cross-app navigation
   */
  private async handleNavigation(payload: any): Promise<void> {
    // Emit navigation event for frontend to handle
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('uaui-navigate', {
        detail: payload
      }));
    }
    console.log(`✅ Navigation triggered:`, payload);
  }

  /**
   * Get available AI providers from UAUI
   */
  async getAvailableProviders(): Promise<string[]> {
    if (!this.uaui) return [];
    return await this.uaui.getAvailableProviders();
  }

  /**
   * Reinitialize UAUI when providers change
   */
  async reinitialize(): Promise<boolean> {
    if (this.uaui) {
      await this.uaui.shutdown();
      this.isInitialized = false;
    }
    return await this.initialize();
  }

  /**
   * Shutdown UAUI
   */
  async shutdown(): Promise<void> {
    if (this.uaui) {
      await this.uaui.shutdown();
      this.uaui = null;
      this.isInitialized = false;
    }
  }

  /**
   * Get default model for provider type
   */
  private getDefaultModel(type: string): string {
    const defaults: Record<string, string> = {
      openai: 'gpt-4',
      groq: 'llama3-70b-8192',
      openrouter: 'openai/gpt-4',
      claude: 'claude-3-opus-20240229',
      gemini: 'gemini-pro',
      mistral: 'mistral-large-latest'
    };
    return defaults[type] || 'default-model';
  }

  /**
   * Check if UAUI is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.uaui !== null;
  }

  /**
   * Get UAUI configuration
   */
  getConfig(): TempoUAUIConfig {
    return this.config;
  }
}

// Create singleton instance
const uauiIntegrationService = new UAUIIntegrationService();

export {
  uauiIntegrationService,
  type TempoUAUIConfig,
  type ChatMessage,
  type UAUIActionResult
};
export default uauiIntegrationService;
