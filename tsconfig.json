{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "resolveJsonModule": true, "downlevelIteration": true, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/lib/*": ["src/lib/*"], "@/stores/*": ["src/stores/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}