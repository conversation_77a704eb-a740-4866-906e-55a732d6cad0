/**
 * Enhanced Dashboard Layout with UAUI Integration
 */

"use client";

import React from 'react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

export function FullDashboardLayoutWithUAUI({ 
  children, 
  title = "UAUI Dashboard",
  description = "Universal AI User Interface Protocol Dashboard"
}: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          {description && (
            <p className="text-muted-foreground mt-2">{description}</p>
          )}
        </div>
        <div className="space-y-6">
          {children}
        </div>
      </div>
    </div>
  );
}

export default FullDashboardLayoutWithUAUI;
