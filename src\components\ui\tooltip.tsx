/**
 * Tooltip component
 */

import * as React from "react"
import { cn } from "../../lib/utils"

interface TooltipContextValue {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const TooltipContext = React.createContext<TooltipContextValue>({
  open: false,
  onOpenChange: () => { }
})

const TooltipProvider = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ children, ...props }, ref) => {
  return <div ref={ref} {...props}>{children}</div>
})
TooltipProvider.displayName = "TooltipProvider"

const Tooltip = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    open?: boolean
    onOpenChange?: (open: boolean) => void
  }
>(({ className, open = false, onOpenChange = () => { }, children, ...props }, ref) => {
  const [internalOpen, setInternalOpen] = React.useState(false)
  const isOpen = open !== undefined ? open : internalOpen

  const handleOpenChange = React.useCallback((newOpen: boolean) => {
    if (open === undefined) {
      setInternalOpen(newOpen)
    }
    onOpenChange(newOpen)
  }, [open, onOpenChange])

  return (
    <TooltipContext.Provider value={{ open: isOpen, onOpenChange: handleOpenChange }}>
      <div ref={ref} className={cn("relative", className)} {...props}>
        {children}
      </div>
    </TooltipContext.Provider>
  )
})
Tooltip.displayName = "Tooltip"

const TooltipTrigger = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    asChild?: boolean
  }
>(({ className, asChild = false, children, ...props }, ref) => {
  const { onOpenChange } = React.useContext(TooltipContext)

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children as React.ReactElement<any>, {
      ...(children.props as any),
      onMouseEnter: (e: React.MouseEvent) => {
        (children.props as any).onMouseEnter?.(e)
        onOpenChange(true)
      },
      onMouseLeave: (e: React.MouseEvent) => {
        (children.props as any).onMouseLeave?.(e)
        onOpenChange(false)
      }
    })
  }

  return (
    <div
      ref={ref}
      className={className}
      onMouseEnter={() => onOpenChange(true)}
      onMouseLeave={() => onOpenChange(false)}
      {...props}
    >
      {children}
    </div>
  )
})
TooltipTrigger.displayName = "TooltipTrigger"

const TooltipContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    side?: "top" | "right" | "bottom" | "left"
    sideOffset?: number
  }
>(({ className, side = "top", sideOffset = 4, ...props }, ref) => {
  const { open } = React.useContext(TooltipContext)

  if (!open) return null

  return (
    <div
      ref={ref}
      className={cn(
        "absolute z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md",
        side === "top" && "bottom-full mb-1",
        side === "bottom" && "top-full mt-1",
        side === "left" && "right-full mr-1",
        side === "right" && "left-full ml-1",
        className
      )}
      style={{
        marginBottom: side === "top" ? sideOffset : undefined,
        marginTop: side === "bottom" ? sideOffset : undefined,
        marginRight: side === "left" ? sideOffset : undefined,
        marginLeft: side === "right" ? sideOffset : undefined,
      }}
      {...props}
    />
  )
})
TooltipContent.displayName = "TooltipContent"

export {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
}
