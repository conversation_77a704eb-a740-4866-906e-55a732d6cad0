/**
 * UAUI Floating Action Button
 * Provides quick access to UAUI system from anywhere in the dashboard
 */

"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  <PERSON>rkles,
  Bot,
  Beaker,
  Settings,
  Brain,
  Activity,
  ChevronUp
} from 'lucide-react';
import { useUAUI } from '@/uaui/use-uaui';
import { cn } from '@/lib/utils';

interface UAUIFloatingButtonProps {
  className?: string;
}

export default function UAUIFloatingButton({ className }: UAUIFloatingButtonProps) {
  const { isInitialized, isLoading, error } = useUAUI();
  const [isOpen, setIsOpen] = useState(false);

  const getStatusColor = () => {
    if (error) return 'bg-red-500';
    if (isInitialized) return 'bg-green-500';
    if (isLoading) return 'bg-yellow-500';
    return 'bg-gray-500';
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (isInitialized) return 'Active';
    if (isLoading) return 'Loading';
    return 'Inactive';
  };

  return (
    <div className={cn("fixed bottom-6 right-6 z-50", className)}>
      <TooltipProvider>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button
                  size="lg"
                  className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 border-0"
                >
                  <div className="relative">
                    <Sparkles className="h-6 w-6 text-white" />
                    {/* Status indicator */}
                    <div className={cn(
                      "absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white",
                      getStatusColor()
                    )} />
                  </div>
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent side="left" className="flex items-center gap-2">
              <span>UAUI System</span>
              <Badge variant="secondary" className="text-xs">
                {getStatusText()}
              </Badge>
            </TooltipContent>
          </Tooltip>

          <DropdownMenuContent
            align="end"
            className="w-64 shadow-xl border-border/50"
            sideOffset={8}
          >
            <DropdownMenuLabel className="flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-primary" />
              <span>UAUI System</span>
              <Badge
                variant={isInitialized ? "default" : error ? "destructive" : "secondary"}
                className="text-xs ml-auto"
              >
                {getStatusText()}
              </Badge>
            </DropdownMenuLabel>

            <DropdownMenuSeparator />

            <DropdownMenuItem asChild>
              <Link href="/dashboard/uaui-system" className="flex items-center gap-2 cursor-pointer">
                <Settings className="h-4 w-4" />
                <span>System Overview</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild>
              <Link href="/dashboard/uaui-system/test" className="flex items-center gap-2 cursor-pointer">
                <Beaker className="h-4 w-4" />
                <span>Test Console</span>
                <Badge variant="outline" className="text-xs ml-auto">Beta</Badge>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem asChild>
              <Link href="/dashboard/ai-providers" className="flex items-center gap-2 cursor-pointer">
                <Bot className="h-4 w-4" />
                <span>AI Providers</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild>
              <Link href="/dashboard/prompt-templates" className="flex items-center gap-2 cursor-pointer">
                <Brain className="h-4 w-4" />
                <span>Prompt Templates</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem className="text-xs text-muted-foreground justify-center">
              Universal AI User Interface
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TooltipProvider>
    </div>
  );
}
