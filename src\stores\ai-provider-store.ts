/**
 * AI Provider Store
 * Manages AI provider state and configuration
 */

import { useState, useCallback } from 'react';

export interface AIProviderConfig {
  id: string;
  name: string;
  type: string;
  apiKey?: string;
  baseURL?: string;
  enabled: boolean;
  priority: number;
  isActive?: boolean;
  isConfigured?: boolean;
}

interface AIProviderStore {
  providers: AIProviderConfig[];
  activeProviders: AIProviderConfig[];
  isLoading: boolean;
  error: string | null;
  addProvider: (provider: AIProviderConfig) => void;
  removeProvider: (id: string) => void;
  updateProvider: (id: string, updates: Partial<AIProviderConfig>) => void;
  toggleProvider: (id: string) => void;
  clearError: () => void;
}

// Simple store implementation using React hooks
export function useAIProviderStore(): AIProviderStore {
  const [providers, setProviders] = useState<AIProviderConfig[]>([
    {
      id: 'openai',
      name: '<PERSON><PERSON><PERSON>',
      type: 'openai',
      enabled: true,
      priority: 1,
      isActive: true,
      isConfigured: true
    },
    {
      id: 'groq',
      name: 'Groq',
      type: 'groq',
      enabled: true,
      priority: 2,
      isActive: true,
      isConfigured: true
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeProviders = providers.filter(p => p.enabled);

  const addProvider = useCallback((provider: AIProviderConfig) => {
    setProviders(prev => [...prev, provider]);
  }, []);

  const removeProvider = useCallback((id: string) => {
    setProviders(prev => prev.filter(p => p.id !== id));
  }, []);

  const updateProvider = useCallback((id: string, updates: Partial<AIProviderConfig>) => {
    setProviders(prev => prev.map(p => p.id === id ? { ...p, ...updates } : p));
  }, []);

  const toggleProvider = useCallback((id: string) => {
    setProviders(prev => prev.map(p => p.id === id ? { ...p, enabled: !p.enabled } : p));
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    providers,
    activeProviders,
    isLoading,
    error,
    addProvider,
    removeProvider,
    updateProvider,
    toggleProvider,
    clearError
  };
}
