/**
 * AI Provider Store
 * Manages AI provider state and configuration
 */

import { useState, useCallback } from 'react';

export interface AIProviderConfig {
  id: string;
  name: string;
  type: string;
  apiKey?: string;
  baseURL?: string;
  enabled: boolean;
  priority: number;
  isActive?: boolean;
  isConfigured?: boolean;
}

interface AIProviderStore {
  providers: AIProviderConfig[];
  activeProviders: AIProviderConfig[];
  isLoading: boolean;
  error: string | null;
  addProvider: (provider: AIProviderConfig) => void;
  removeProvider: (id: string) => void;
  updateProvider: (id: string, updates: Partial<AIProviderConfig>) => void;
  toggleProvider: (id: string) => void;
  clearError: () => void;
  createProvider: (provider: Omit<AIProviderConfig, 'id'>) => void;
  fetchProviders: () => Promise<void>;
}

// Simple store implementation using React hooks
export function useAIProviderStore(): AIProviderStore {
  const [providers, setProviders] = useState<AIProviderConfig[]>([
    {
      id: 'openai',
      name: 'OpenAI',
      type: 'openai',
      enabled: true,
      priority: 1,
      isActive: true,
      isConfigured: true
    },
    {
      id: 'groq',
      name: 'Groq',
      type: 'groq',
      enabled: true,
      priority: 2,
      isActive: true,
      isConfigured: true
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeProviders = providers.filter(p => p.enabled);

  const addProvider = useCallback((provider: AIProviderConfig) => {
    setProviders(prev => [...prev, provider]);
  }, []);

  const removeProvider = useCallback((id: string) => {
    setProviders(prev => prev.filter(p => p.id !== id));
  }, []);

  const updateProvider = useCallback((id: string, updates: Partial<AIProviderConfig>) => {
    setProviders(prev => prev.map(p => p.id === id ? { ...p, ...updates } : p));
  }, []);

  const toggleProvider = useCallback((id: string) => {
    setProviders(prev => prev.map(p => p.id === id ? { ...p, enabled: !p.enabled } : p));
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const createProvider = useCallback((provider: Omit<AIProviderConfig, 'id'>) => {
    const newProvider: AIProviderConfig = {
      ...provider,
      id: `provider-${Date.now()}`
    };
    setProviders(prev => [...prev, newProvider]);
  }, []);

  const fetchProviders = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch providers');
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    providers,
    activeProviders,
    isLoading,
    error,
    addProvider,
    removeProvider,
    updateProvider,
    toggleProvider,
    clearError,
    createProvider,
    fetchProviders
  };
}
